<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
 <head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="ProgId" content="Excel.Sheet">
  <meta name="Generator" content="WPS Office ET">
  <link id="Main-File" rel="Main-File" href="../人间道-创业服务平台1.1.html">
  <link rel="File-List" href="filelist.xml">
  <link rel="Stylesheet" href="stylesheet.css">
  <style>
<!-- @page
	{margin:1.00in 0.75in 1.00in 0.55in;
	mso-header-margin:0.51in;
	mso-footer-margin:0.51in;
	mso-page-orientation:landscape;}
 -->  </style>
  <!--[if gte mso 9]>
   <xml>
    <x:WorksheetOptions>
     <x:DefaultRowHeight>306</x:DefaultRowHeight>
     <x:Panes>
      <x:Pane>
       <x:Number>3</x:Number>
       <x:ActiveCol>7</x:ActiveCol>
       <x:ActiveRow>1</x:ActiveRow>
       <x:RangeSelection>H2</x:RangeSelection>
      </x:Pane>
     </x:Panes>
     <x:ProtectContents>False</x:ProtectContents>
     <x:ProtectObjects>False</x:ProtectObjects>
     <x:ProtectScenarios>False</x:ProtectScenarios>
     <x:PageBreakZoom>100</x:PageBreakZoom>
     <x:Zoom>87</x:Zoom>
     <x:Print>
      <x:ValidPrinterInfo/>
      <x:PaperSizeIndex>9</x:PaperSizeIndex>
     </x:Print>
    </x:WorksheetOptions>
   </xml>
  <![endif]-->
  <script language="JavaScript">
	if (window.name!="frSheet")
		window.location.replace("../人间道-创业服务平台1.1.html");
	else
		parent.fnUpdateTabs(0);
</script>
 </head>
 <body link="blue" vlink="purple" class="xl172">
  <table width="2043.15" border="0" cellpadding="0" cellspacing="0" style='width:681.05pt;border-collapse:collapse;table-layout:fixed;'>
   <col width="112.05" class="xl172" style='mso-width-source:userset;mso-width-alt:1593;'/>
   <col width="166.50" class="xl172" style='mso-width-source:userset;mso-width-alt:2368;'/>
   <col width="260.25" class="xl172" style='mso-width-source:userset;mso-width-alt:3701;'/>
   <col width="267.15" class="xl172" style='mso-width-source:userset;mso-width-alt:3799;'/>
   <col width="823.50" class="xl172" style='mso-width-source:userset;mso-width-alt:11712;'/>
   <col width="413.70" class="xl172" style='mso-width-source:userset;mso-width-alt:5883;'/>
   <col width="169.50" span="249" class="xl172" style='mso-width-source:userset;mso-width-alt:2410;'/>
   <col width="169.50" class="xl172" style='mso-width-source:userset;mso-width-alt:2410;'/>
   <tr height="91.95" style='height:30.65pt;'>
    <td class="xl173" height="91.95" width="112.05" style='height:30.65pt;width:37.35pt;' x:str>平台功能</td>
    <td class="xl173" width="166.50" style='width:55.50pt;' x:str>服务类型</td>
    <td class="xl173" width="260.25" style='width:86.75pt;' x:str>服务项目</td>
    <td class="xl173" width="267.15" style='width:89.05pt;' x:str>服务内容</td>
    <td class="xl173" width="823.50" style='width:274.50pt;' x:str>服务流程</td>
    <td class="xl173" width="413.70" style='width:137.90pt;' x:str>备注</td>
   </tr>
   <tr height="597" style='height:199.00pt;'>
    <td class="xl174" height="964.95" rowspan="2" style='height:321.65pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>前<br/><br/>台</td>
    <td class="xl175" rowspan="2" style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>直<br/><br/>营</td>
    <td class="xl176" x:str>1、地址挂靠服务</td>
    <td class="xl177" x:str>为客户提供公司注册需要的地址</td>
    <td class="xl178" x:str>客户<font class="font23">→</font><font class="font0">选择区域→</font><font class="font24">上传（字段）</font><font class="font0">：</font><font class="font24">原企业执照（非必要）、法人身份证（必要）、电话（必要）</font><font class="font0">→支付费用→系统自动生成→系统自动生成地址材料→客户端显示系统进度，下载地址材料</font><font class="font24">（地址材料包括（字段）：地址提供者房屋的租赁合同、房产证、土地证，以及自动生成的“办公场地共享协议”）</font><font class="font0">→客户公司注册好后上“新的传营业执照”（线下操作，然后回传执照）→客户端转到下一步-》显示可预约上门服务（两种：1.开户跳页面 2.自己上传信息（银行提供的下户时间））（1年内可免费约两次，第三次起需再支付费用）/或者开户-》通知地址提供者，确定是否可上门→时时上门服务情况评价→次年续费通知。</font></td>
    <td class="xl179"></td>
   </tr>
   <tr height="367.95" style='height:122.65pt;'>
    <td class="xl176" x:str>2、银行开户服务</td>
    <td class="xl181" x:str>为客户企业提供银行开户服务</td>
    <td class="xl178" x:str>客户→选择区域→选择银行-》（未从地址过来是：需自己填写红色字段）→支付费用→系统推送信息给“银行经理”<font class="font24">（推送的信息内容(字段)：新企业执照、法人身份证、电话；本字段内容可以从系统自动调取（从地址过来））</font><font class="font0">→银行经理回复“下户时间”→系统将下户时间推送给客户和地址提供者→地址提供者和客户确认“下户时间”（预约上门）→时时评价。</font></td>
    <td class="xl179"></td>
   </tr>
   <![if supportMisalignedColumns]>
    <tr width="0" style='display:none;'>
     <td width="112" style='width:37;'></td>
     <td width="167" style='width:56;'></td>
     <td width="260" style='width:87;'></td>
     <td width="267" style='width:89;'></td>
     <td width="824" style='width:275;'></td>
     <td width="414" style='width:138;'></td>
     <td width="170" style='width:57;'></td>
     <td width="170" style='width:57;'></td>
    </tr>
   <![endif]>
  </table>
 </body>
</html>
