<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spring Boot 入门指南</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1, h2, h3 { color: #2c3e50; }
        h1 { border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #ccc; padding-bottom: 5px; }
        code { background-color: #f4f4f4; padding: 2px 6px; border-radius: 4px; font-family: "Courier New", Courier, monospace; }
        pre { background-color: #2d2d2d; color: #f8f8f2; padding: 15px; border-radius: 5px; overflow-x: auto; }
        pre code { background-color: transparent; padding: 0; }
        .container { background-color: #fff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .concept { margin-bottom: 15px; padding-left: 20px; border-left: 4px solid #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Spring Boot 核心概念与入门实践</h1>

        <h2>一、 Spring Boot 是什么？</h2>
        <p>Spring Boot 是一个由 Pivotal 团队提供的全新框架，其设计目的是用来简化新 Spring 应用的初始搭建以及开发过程。它基于 Spring 框架，但通过“<strong>约定优于配置</strong>”(Convention over Configuration)的理念，让开发者可以快速地创建出独立、生产级的、基于 Spring 的应用程序。</p>
        <p>您可以把它理解为一个“脚手架”，它预先配置好了很多东西，让您不必再为繁琐的XML配置而烦恼，可以更专注于业务逻辑的开发。</p>

        <h2>二、 核心思想与主要概念</h2>
        <p>Spring Boot 的核心思想在于自动化配置和快速开发。以下是几个最重要的概念：</p>
        
        <div class="concept">
            <h3>1. 自动配置 (Auto-Configuration)</h3>
            <p>这是 Spring Boot 最神奇的特性。它会根据你项目中引入的依赖（JAR包），自动为你配置好应用程序。例如，如果你在项目中加入了 <code>spring-boot-starter-web</code> 依赖，Spring Boot 会自动配置好 Tomcat 服务器和 Spring MVC。你几乎不需要写任何配置代码。</p>
        </div>

        <div class="concept">
            <h3>2. “起步依赖” (Starters)</h3>
            <p>Starters 是一组方便的依赖描述符。你只需要在你的项目中引入一个 starter，它就会把所有相关的技术依赖都自动包含进来，从而避免了手动添加和管理大量依赖版本的麻烦。常见的 Starters 有：</p>
            <ul>
                <li><code>spring-boot-starter-web</code>: 用于构建 Web 应用，包括 RESTful 应用。</li>
                <li><code>spring-boot-starter-data-jpa</code>: 用于使用 Spring Data JPA 和 Hibernate。</li>
                <li><code>spring-boot-starter-test</code>: 用于编写单元测试和集成测试。</li>
            </ul>
        </div>

        <div class="concept">
            <h3>3. 内嵌服务器 (Embedded Servers)</h3>
            <p>Spring Boot 内嵌了如 Tomcat, Jetty, or Undertow 等 Web 服务器。这意味着你不需要将你的应用打包成 WAR 文件部署到外部服务器上。你可以直接将应用打包成一个可执行的 JAR 文件，通过 <code>java -jar</code> 命令就能运行。</p>
        </div>

        <div class="concept">
            <h3>4. Actuator (应用监控)</h3>
            <p>Spring Boot Actuator 提供了生产级的监控和管理功能。通过简单的配置，你可以通过 HTTP 端点查看应用的健康状况、Bean 的加载情况、环境变量、日志信息等。</p>
        </div>

        <h2>三、 简单的入门案例：Hello World</h2>
        <p>下面我们通过一个简单的 Web 应用来展示如何使用 Spring Boot。这个应用将提供一个 HTTP 接口，访问时返回 "Hello, Spring Boot!"。</p>

        <h3>步骤 1: 创建一个 Maven 项目并添加依赖</h3>
        <p>首先，创建一个标准的 Maven 项目。然后在 <code>pom.xml</code> 文件中添加以下内容。我们需要 <code>spring-boot-starter-parent</code> 来管理版本，以及 <code>spring-boot-starter-web</code> 来支持Web开发。</p>
        <pre><code>&lt;?xml version="1.0" encoding="UTF-8"?&gt;
&lt;project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"&gt;
    &lt;modelVersion&gt;4.0.0&lt;/modelVersion&gt;

    &lt;groupId&gt;com.example&lt;/groupId&gt;
    &lt;artifactId&gt;demo&lt;/artifactId&gt;
    &lt;version&gt;0.0.1-SNAPSHOT&lt;/version&gt;

    &lt;parent&gt;
        &lt;groupId&gt;org.springframework.boot&lt;/groupId&gt;
        &lt;artifactId&gt;spring-boot-starter-parent&lt;/artifactId&gt;
        &lt;version&gt;2.7.5&lt;/version&gt; &lt;!-- 你可以选择一个较新的稳定版本 --&gt;
        &lt;relativePath/&gt; &lt;!-- lookup parent from repository --&gt;
    &lt;/parent&gt;

    &lt;dependencies&gt;
        &lt;dependency&gt;
            &lt;groupId&gt;org.springframework.boot&lt;/groupId&gt;
            &lt;artifactId&gt;spring-boot-starter-web&lt;/artifactId&gt;
        &lt;/dependency&gt;
    &lt;/dependencies&gt;

    &lt;build&gt;
        &lt;plugins&gt;
            &lt;plugin&gt;
                &lt;groupId&gt;org.springframework.boot&lt;/groupId&gt;
                &lt;artifactId&gt;spring-boot-maven-plugin&lt;/artifactId&gt;
            &lt;/plugin&gt;
        &lt;/plugins&gt;
    &lt;/build&gt;
&lt;/project&gt;</code></pre>

        <h3>步骤 2: 创建主应用程序类</h3>
        <p>创建一个 Java 类作为应用的入口。这个类需要使用 <code>@SpringBootApplication</code> 注解。</p>
        <pre><code>package com.example.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class DemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);
    }

}</code></pre>
        <p><code>@SpringBootApplication</code> 是一个复合注解，它包含了 <code>@Configuration</code>, <code>@EnableAutoConfiguration</code> 和 <code>@ComponentScan</code>。</p>

        <h3>步骤 3: 创建一个 Controller</h3>
        <p>创建一个控制器来处理 HTTP 请求。</p>
        <pre><code>package com.example.demo;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class HelloController {

    @GetMapping("/")
    public String hello() {
        return "Hello, Spring Boot!";
    }
}
</code></pre>
        <ul>
            <li><code>@RestController</code>: 标记这个类是一个处理 RESTful 请求的控制器，并且类中所有方法的返回值都会被直接写入 HTTP 响应体中。</li>
            <li><code>@GetMapping("/")</code>: 将 HTTP GET 请求映射到 <code>/</code> 路径下的 <code>hello()</code> 方法。</li>
        </ul>

        <h3>步骤 4: 运行应用</h3>
        <p>现在你可以运行你的应用了。最简单的方式是直接在你的 IDE 中运行 <code>DemoApplication</code> 的 <code>main</code> 方法。</p>
        <p>或者，你可以在项目根目录下打开终端，使用 Maven 插件来运行：</p>
        <pre><code>mvn spring-boot:run</code></pre>
        <p>应用启动后，打开浏览器访问 <code>http://localhost:8080</code>，你将会看到页面上显示 "Hello, Spring Boot!"。</p>

        <h2>四、 总结</h2>
        <p>恭喜你！你已经完成了第一个 Spring Boot 应用。这个简单的例子展示了 Spring Boot 的强大之处：极少的配置、快速的启动和简单的部署。希望这个文档能帮助你迈出学习 Spring Boot 的第一步。</p>

    </div>
</body>
</html>